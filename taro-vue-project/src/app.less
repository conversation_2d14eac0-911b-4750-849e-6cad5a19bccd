/* 全局样式文件 */

/* 移动端适配基础样式 */
* {
  box-sizing: border-box;
}

html,
body {
  margin: 0;
  padding: 0;
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", "Roboto", "Oxygen",
    "Ubuntu", "Cantarell", "Fira Sans", "Droid Sans", "Helvetica Neue",
    sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* H5端移动适配 */
@media screen and (max-width: 750px) {
  html {
    font-size: 40px; /* 基础字体大小，配合rem使用 */
  }
}

@media screen and (min-width: 751px) {
  html {
    font-size: 40px;
  }
}

/* 确保页面在移动端正确显示 */
#app {
  width: 100%;
  min-height: 100vh;
}

taro-button-core + taro-button-core {
  margin-top: 0 !important;
}
