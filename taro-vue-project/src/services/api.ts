// API服务接口 - 从WePY2的services.js迁移到Taro
import { post } from "./request";

// ==================== 类型定义 ====================

// 评论相关类型
export interface CommentItem {
  Name: string;
  AvatarUrl?: string;
  CreateDate: string;
  Rating: number;
  Comment: string;
  SystemReply?: string;
}

export interface CommentListParams {
  ProductCode: string;
  PageIndex?: number;
  PageSize?: number;
  Filter?: "all" | "good" | "medium" | "bad";
}

export interface CommentStatistics {
  Total: number;
  GoodCount: number;
  MediumCount: number;
  BadCount: number;
}

// 拼团相关类型
export interface GroupMember {
  MemberName: string;
  AvatarUrl?: string;
  GroupNo: string;
}

export interface GroupDetail {
  GroupNo: string;
  GroupMembers: GroupMember[];
  MemberQty: number;
  MemberCount: number;
  MilliSeconds: number;
}

// 服务机构相关类型
export interface Institution {
  Name: string;
  OperationHour: string;
  Address: string;
  Contact: Array<{ ContactNo: string }>;
}

export interface Doctor {
  Name: string;
  ImageUrl: string;
  JobTitle: string;
  Department: string;
}

// 商品提醒类型
export interface ProductTip {
  Title: string;
  Content: string;
}

// 礼包商品类型
export interface PackageItem {
  ProductCode: string;
  Name: string;
  Image: string;
  Quantity: number;
  Sales: number;
  Product: string;
}

// 推荐商品类型
export interface RecommendProduct {
  ProductCode: string;
  Name: string;
  Image: string;
  UnitPrice: string;
  RetailPrice?: string;
  Product: string;
}

/**
 * 首页轮播
 * @returns
 */
export function homeBanner() {
  return post("/HomepageSlide");
}

/**
 * 首页栏目列表
 * @returns
 */
export function menuList() {
  return post("/HomepageMenu");
}

/**
 * 首页分类列表
 * @returns
 */
export function homeCategoryList() {
  return post("/homepageCategory");
}

/**
 * 所有分类
 * @returns
 */
export function categoryAllList() {
  return post("/Category");
}

/**
 * 一级分类
 * @returns
 */
export function categoryList() {
  return post("/CategoryList");
}

/**
 * 二级分类
 * @returns
 */
export function category2List(data) {
  return post("/CategoryLevel1", { data });
}
/**
 * 三级分类
 * @param {*} data
 * @returns
 */
export function category3List(data) {
  return post("/CategoryLevel2", { data });
}

/**
 * 产品列表
 * @param {*} data
 * @returns
 */
export function getProductList(data) {
  return post("/Product", { data });
}

/**
 * 获取商品详情
 * @param {*} data
 */
export function getProDetail(data) {
  return post("/ProductDetails", { data });
}

/**
 * 商品详情轮播图
 * @param {*} data
 * @returns
 */
export function getProBanner(data) {
  return post("/ProductRotateImage", { data });
}

/**
 * 产品数量购买价格
 * @param {*} data
 * @returns
 */
export function getProQtyPurchase(data) {
  return post("/ProductQtyPurchase", { data });
}

/**
 * 产品规格选项
 * @param {*} data
 * @returns
 */
export function getProductOption(data) {
  return post("/ProductOption", { data });
}

/**
 * 国家产品规格选项
 * @param {*} data
 * @returns
 */
export function ProductOptionCountry(data) {
  return post("/ProductOptionCountry", { data });
}

/**
 * 规格选项组合
 * @param {*} data
 * @returns
 */
export function ProductSpecCombination(data) {
  return post("/ProductSpecCombination", { data });
}

/**
 * 产品规格价格
 * @param {*} data
 * @returns
 */
export function ProductSKU(data) {
  return post("/ProductSKU", { data });
}

/**
 * 加入购物车
 * @param {*} data
 * @returns
 */
export function AddToCart(data) {
  return post("/AddToCart", { data });
}

/**
 * 更新购物车
 * @param {*} data
 * @returns
 */
export function UpdateToCart(data) {
  return post("/UpdateToCart", { data });
}

/**
 * 购物车列表
 * @param {*} data
 * @returns
 */
export function GetShoppingCart(data) {
  return post("/GetShoppingCart", { data });
}

/**
 * 提交订单
 * @param {*} data
 * @returns
 */
export function Checkout(data) {
  return post("/Checkout", { data });
}

/**
 * 支付
 * @param {*} data
 * @returns
 */
export function SaveCart(data) {
  return post("/SaveCart", { data });
}

/**
 * 待支付里继续支付
 * @param {*} data
 * @returns
 */
export function CompletePayment(data) {
  return post("/CompletePayment", { data });
}

/**
 * 订单列表
 * @param {*} data
 * @returns
 */
export function DispurList(data) {
  return post("/DispurList", { data });
}

/**
 * 订单详情
 * @param {*} data
 * @returns
 */
export function DispurDetail(data) {
  return post("/DispurDetail", { data });
}

/**
 * 查询快递单号
 * @param {*} data
 * @returns
 */
export function CheckTrackingNo(data) {
  return post("/CheckTrackingNo", { data });
}

/**
 * 确认收货
 * @param {*} data
 * @returns
 */
export function UpdateReceive(data) {
  return post("/UpdateReceive", { data });
}

/**
 * 取消待付款订单
 * @param {*} data
 * @returns
 */
export function CancelPendingPaymentSales(data) {
  return post("/CancelPendingPaymentSales", { data });
}

/**
 * 退货订单
 * @param {*} data
 * @returns
 */
export function DispurListForStockReturn(data) {
  return post("/DispurListForStockReturn", { data });
}

/**
 * 退货订单详细
 * @param {*} data
 * @returns
 */
export function DispurDetailForStockReturn(data) {
  return post("/DispurDetailForStockReturn", { data });
}

/**
 * 退货原因列表
 * @param {*} data
 * @returns
 */
export function GetReason(data) {
  return post("/GetReason", { data });
}

/**
 * 物流公司编码列表
 * @param {*} data
 * @returns
 */
export function GetCourierList(data) {
  return post("/GetCourierList", { data });
}

/**
 * 确认收货换货商品
 * @param {*} data
 * @returns
 */
export function UpdateReceiveReturn(data) {
  return post("/UpdateReceiveReturn", { data });
}

/**
 * 用户退货填写追踪编号和公司编码
 * @param {*} data
 * @returns
 */
export function UpdateStockReturnStatus(data) {
  return post("/UpdateStockReturnStatus", { data });
}

/**
 * 退货退款数额
 * @param {*} data
 * @returns
 */
export function StockReturnRefundAmt(data) {
  return post("/StockReturnRefundAmt", { data });
}

/**
 * 确认退货
 * @param {*} data
 * @returns
 */
export function SaveReturnRefund(data) {
  return post("/SaveReturnRefund", { data });
}

/**
 * 提醒发货
 * @param {*} data
 * @returns
 */
export function DeliveryNotice(data) {
  return post("/DeliveryNotice", { data });
}

/**
 * 七牛Token
 * @param {*} data
 * @returns
 */
export function QiniuToken(data) {
  return post("/QiniuToken", { data });
}

/**
 * 提现数据
 * @param {*} data
 * @returns
 */
export function WithdrawalInfo(data) {
  return post("/WithdrawalInfo", { data });
}

/**
 * 提现
 * @param {*} data
 * @returns
 */
export function EWalletWithdrawal(data) {
  return post("/EWalletWithdrawal", { data });
}

/**
 * 提现列表
 * @param {*} data
 * @returns
 */
export function WithdrawalListing(data) {
  return post("/WithdrawalListing", { data });
}

/**
 * 产品搜索
 * @param {*} data
 * @returns
 */
export function SearchProduct(data) {
  return post("/SearchProduct", { data });
}

/**
 * 免费商品列表
 * @param {*} data
 * @returns
 */
export function getFreePro(data) {
  return post("/FOCProduct", { data });
}

/**
 * 优惠礼包列表
 * @param {*} data
 * @returns
 */
export function getPackage(data) {
  return post("/Package", { data });
}

/**
 * 优惠礼包内容
 * @param {*} data
 * @returns
 */
export function getPackageItem(data) {
  return post("/PackageSubItem", { data });
}

/**
 * 国家列表
 * @param {*} data
 * @returns
 */
export function getCountry() {
  return post("/GetCountry");
}

/**
 * 省列表
 * @param {*} data
 * @returns
 */
export function getState(data) {
  return post("/GetState", { data });
}

/**
 * 市列表
 * @param {*} data
 * @returns
 */
export function getCity(data) {
  return post("/GetCity", { data });
}

/**
 * 区列表
 * @param {*} data
 * @returns
 */
export function getDistrict(data) {
  return post("/GetDistrict", { data });
}

/**
 * 获取验证码
 * @param {*} data
 * @returns
 */
export function getCode(data) {
  return post("/RequestVerificationCode", { data });
}

/**
 * 注册
 * @param {*} data
 * @returns
 */
export function fetchRegister(data) {
  return post("/RegisterMember", { data });
}

/**
 * 微信注册
 * @param {*} data
 * @returns
 */
export function WechatRegisterMember(data) {
  return post("/WechatRegisterMember", { data });
}

/**
 * 登录
 * @param {*} data
 * @returns
 */
export function fetchLogin(data) {
  return post("/Login", { data });
}

/**
 * code换取用户手机号
 * @param {*} data
 * @returns
 */
export function GetPhoneNo(data) {
  return post("/GetPhoneNo", { data });
}

/**
 * 登出
 * @param {*} data
 * @returns
 */
export function Logout(data) {
  return post("/Logout", { data });
}

/**
 * 用户信息
 * @param {*} data
 * @returns
 */
export function getUserInfo(data) {
  return post("/MemberProfile", { data });
}

/**
 * 修改用户信息
 * @param {*} data
 * @returns
 */
export function EditProfile(data) {
  return post("/ChangeProfile", { data });
}

/**
 * 银行列表
 * @param {*} data
 * @returns
 */
export function getBankList(data) {
  return post("/GetBank", { data });
}

/**
 * 收邮地址
 * @param {*} data
 * @returns
 */
export function DeliveryAddressList(data) {
  return post("/DeliveryAddressList", { data });
}

/**
 * 添加收邮地址
 * @param {*} data
 * @returns
 */
export function AddDeliveryAddress(data) {
  return post("/AddDeliveryAddress", { data });
}

/**
 * 修改收邮地址
 * @param {*} data
 * @returns
 */
export function EditDeliveryAddress(data) {
  return post("/EditDeliveryAddress", { data });
}

/**
 * 删除收邮地址
 * @param {*} data
 * @returns
 */
export function DeleteDeliveryAddress(data) {
  return post("/DeleteDeliveryAddress", { data });
}

/**
 * 修改会员登录密码
 * @param {*} data
 * @returns
 */
export function editPsw(data) {
  return post("/ChangePassword", { data });
}

/**
 * 重置会员登录密码
 * @param {*} data
 * @returns
 */
export function ResetPassword(data) {
  return post("/ResetPassword", { data });
}

/**
 * 修改会员二级密码
 * @param {*} data
 * @returns
 */
export function edit2Psw(data) {
  return post("/ChangeSecondaryPassword", { data });
}

/**
 * 直属粉丝
 * @param {*} data
 * @returns
 */
export function getFansList(data) {
  return post("/DirectSponsor", { data });
}

/**
 * 团队
 * @param {*} data
 * @returns
 */
export function getGroupList(data) {
  return post("/SponsorGroup", { data });
}

/**
 * 电子钱包余额
 * @param {*} data
 * @returns
 */
export function eWalletBalance(data) {
  return post("/eWalletBalance", { data });
}

/**
 * 购物钱包余额
 * @param {*} data
 * @returns
 */
export function SWalletBalance(data) {
  return post("/SWalletBalance", { data });
}

/**
 * 电子钱包明细
 * @param {*} data
 * @returns
 */
export function EWalletStatement(data) {
  return post("/EWalletStatement", { data });
}

/**
 * 购物钱包明细
 * @param {*} data
 * @returns
 */
export function SWalletStatement(data) {
  return post("/SWalletStatement", { data });
}

/**
 * 收益明细
 * @param {*} data
 * @returns
 */
export function BonusSum(data) {
  return post("/Listing_BonusSum", { data });
}

/**
 * 分享奖利
 * @param {*} data
 * @returns
 */
export function ShareBonus(data) {
  return post("/Listing_ShareBonus", { data });
}

/**
 * 感恩奖利
 * @param {*} data
 * @returns
 */
export function AppreciateBonus(data) {
  return post("/Listing_AppreciateBonus", { data });
}

/**
 * 分享交付机构收入
 * @param {*} data
 * @returns
 */
export function SponsorSupplierBonus(data) {
  return post("/Listing_SponsorSupplierBonus", { data });
}

/**
 * 综合奖利
 * @param {*} data
 * @returns
 */
export function GroupSharingBonus(data) {
  return post("/Listing_GroupSharingBonus", { data });
}

/**
 * 推广费4奖励
 * @param {*} data
 * @returns
 */
// eslint-disable-next-line camelcase
export function Listing_Promote4(data) {
  return post("/Listing_Promote4", { data });
}

/**
 * 红包明细
 * @param {*} data
 * @returns
 */
// eslint-disable-next-line camelcase
export function Listing_RandomRebate(data) {
  return post("/Listing_RandomRebate", { data });
}

/**
 * 首页红包详情
 * @returns
 */
// eslint-disable-next-line camelcase
export function Listing_AngpaoBonusDetail(data) {
  return post("/Listing_AngpaoBonusDetail", { data });
}

/**
 * 查看返红包
 * @returns
 */
// eslint-disable-next-line camelcase
export function CheckAngPao(data) {
  return post("/CheckAngPao", { data });
}

/**
 * AccessToken
 * @returns
 */
export function AccessToken() {
  return post("/AccessToken");
}

/**
 * 获取直播
 * @returns
 */
export function GetLiveInfo(data) {
  return post("/GetLiveInfo", { data });
}

/**
 * 小程序信息内容
 * @returns
 */
export function GetInfoContent(data) {
  return post("/GetInfoContent", { data });
}

/**
 * 评价
 * @returns
 */
export function AddProductComment(data) {
  return post("/AddProductComment", { data });
}

/**
 * 评价列表
 * @returns
 */
export function ProductCommentList(data) {
  return post("/ProductCommentList", { data });
}

/**
 * 签到
 * @returns
 */
export function Checkin(data) {
  return post("/Checkin", { data });
}

/**
 * 签到状态
 * @returns
 */
export function CheckinChecking(data) {
  return post("/CheckinChecking", { data });
}

/**
 * 爆品推荐产品
 * @returns
 */
export function FeaturedProduct(data) {
  return post("/FeaturedProduct", { data });
}

/**
 * 品牌折扣产品
 * @returns
 */
export function RebateProduct(data) {
  return post("/RebateProduct", { data });
}

/**
 * 限时抢购产品
 * @returns
 */
export function LimitedTimeProduct(data) {
  return post("/LimitedTimeProduct", { data });
}

/**
 * 拼团秒杀产品
 * @returns
 */
export function GroupPurchaseProduct(data) {
  return post("/GroupPurchaseProduct", { data });
}

/**
 * 国家品牌
 * @returns
 */
export function CountryBrand(data) {
  return post("/CountryBrand", { data });
}

/**
 * 国家品牌子分类列表
 * @returns
 */
export function CategoryListByCountryBrand(data) {
  return post("/CategoryListByCountryBrand", { data });
}

/**
 * 国家品牌，分类和子分类列表
 * @returns
 */
export function CountryBrandCategory(data) {
  return post("/CountryBrandCategory", { data });
}

// 获取品牌
export function CategoryBrand(data) {
  return post("/CategoryBrand", { data });
}

/**
 * 浏览记录
 * @returns
 */
export function StoreBrowseData(data) {
  return post("/StoreBrowseData", { data });
}

/**
 * 推荐产品
 * @returns
 */
export function RecommendedProduct(data) {
  return post("/RecommendedProduct", { data });
}

/**
 * 预热限时抢购产品
 * @returns
 */
export function PreLimitedTimeProduct(data) {
  return post("/PreLimitedTimeProduct", { data });
}

/**
 * 预热拼团秒杀产品
 * @returns
 */
export function PreGroupPurchaseProduct(data) {
  return post("/PreGroupPurchaseProduct", { data });
}

/**
 * 余额时间
 * @returns
 */
export function GetTimeBalance(data) {
  return post("/GetTimeBalance", { data });
}

/**
 * 发起拼团
 * @returns
 */
export function CreatePurchaseGroup(data) {
  return post("/CreatePurchaseGroup", { data });
}

/**
 * 参加拼团
 * @returns
 */
export function JoinPurchaseGroup(data) {
  return post("/JoinPurchaseGroup", { data });
}

/**
 * 可参拼团列表
 * @returns
 */
export function PurchaseGroupList(data) {
  return post("/PurchaseGroupList", { data });
}

/**
 * 拼团详细
 * @returns
 */
export function PurchaseGroup(data) {
  return post("/PurchaseGroup", { data });
}

/**
 * 产品拼团详细
 * @returns
 */
export function PurchaseGroupByProductCode(data) {
  return post("/PurchaseGroupByProductCode", { data });
}

/**
 * 首页国家品牌栏目
 * @returns
 */
export function HomePageCountryBrand(data) {
  return post("/HomePageCountryBrand", { data });
}

/**
 * 团购结帐
 * @returns
 */
export function CheckOutPurchaseGroup(data) {
  return post("/CheckOutPurchaseGroup", { data });
}

/**
 * 团购确认结帐
 * @returns
 */
export function SaveCartPurchaseGroup(data) {
  return post("/SaveCartPurchaseGroup", { data });
}

/**
 * 团购完成支付
 * @returns
 */
export function CompletePaymentPurchaseGroup(data) {
  return post("/CompletePaymentPurchaseGroup", { data });
}

/**
 * 取消退货
 * @returns
 */
export function CancelStockReturn(data) {
  return post("/CancelStockReturn", { data });
}

/**
 * 预热产品详细
 */
export function PreProductDetails(data) {
  return post("/PreProductDetails", { data });
}

/**
 * 产品提示
 */
export function ProductReminder(data) {
  return post("/ProductReminder", { data });
}

/**
 * 检查登录状态
 */
export function CheckLogin(data) {
  return post("/CheckLogin", { data });
}

/**
 * 预热产品
 */
export function PreProductList(data) {
  return post("/PreProductList", { data });
}

/**
 * 预热产品规格选项
 */
export function PreProductOption(data) {
  return post("/PreProductOption", { data });
}

/**
 * 网站信息
 * @param {*} data
 * @returns
 */
export function NewsListing(data) {
  return post("/NewsListing", { data });
}

/**
 * 拼团编号
 * @param {*} data
 * @returns
 */
export function GetGroupNo(data) {
  return post("/GetGroupNo", { data });
}

/**
 * 正式订单号
 * @param {*} data
 * @returns
 */
export function GetActualBillNo(data) {
  return post("/GetActualBillNo", { data });
}

/**
 * 电子钱包充值
 * @param {*} data
 * @returns
 */
export function EWalletTopUp(data) {
  return post("/EWalletTopUp", { data });
}

/**
 * 订阅活动开始推送
 * @param {*} data
 * @returns
 */
export function SubscribeEvent(data) {
  return post("/SubscribeEvent", { data });
}

/**
 * 订阅到货推送
 * @param {*} data
 * @returns
 */
export function SubscribeStock(data) {
  return post("/SubscribeStock", { data });
}

/**
 * 添加实名
 * @param {*} data
 * @returns
 */
export function AddRealName(data) {
  return post("/AddRealName", { data });
}

/**
 * 更改实名
 * @param {*} data
 * @returns
 */
export function EditRealName(data) {
  return post("/EditRealName", { data });
}

/**
 * 查看实名
 * @param {*} data
 * @returns
 */
export function ViewRealName(data) {
  return post("/ViewRealName", { data });
}

/**
 * 身份证识别
 * @param {*} data
 * @returns
 */
export function IDCardOCR(data) {
  return post("/IDCardOCR", { data });
}

/**
 * 代理商初始充值
 * @param {*} data
 * @returns
 */
export function SFWalletTopUpSC(data) {
  return post("/SFWalletTopUpSC", { data });
}

/**
 * 联创注册
 * @param {*} data
 * @returns
 */
export function WechatRegisterUF(data) {
  return post("/WechatRegisterUF", { data });
}

/**
 * 联创级别列表
 * @param {*} data
 * @returns
 */
export function GetUFType(data) {
  return post("/GetUFType", { data });
}

/**
 * 联创组列表
 * @param {*} data
 * @returns
 */
export function GetUFList(data) {
  return post("/GetUFList", { data });
}

/**
 * 爆品钱包充值
 * @param {*} data
 * @returns
 */
export function FWalletTopUp(data) {
  return post("/FWalletTopUp", { data });
}

/**
 * 充值成为联创
 * @param {*} data
 * @returns
 */
export function FWalletTopUpJoinUF(data) {
  return post("/FWalletTopUpJoinUF", { data });
}

/**
 * 联创团队
 * @param {*} data
 * @returns
 */
export function UFGroup(data) {
  return post("/UFGroup", { data });
}

/**
 * 爆品钱包余额
 * @param {*} data
 * @returns
 */
export function FWalletBalance(data) {
  return post("/FWalletBalance", { data });
}

/**
 * 爆品钱包明细
 * @param {*} data
 * @returns
 */
export function FWalletStatement(data) {
  return post("/FWalletStatement", { data });
}

/**
 * 爆品特奖
 * @param {*} data
 * @returns
 */
export function ListingFeaturedBonus(data) {
  return post("/Listing_FeaturedBonus", { data });
}

/**
 * 红包收入
 * @param {*} data
 * @returns
 */
export function ListingAngpaoBonus(data) {
  return post("/Listing_AngpaoBonus", { data });
}

/**
 * 至尊加油收入
 * @param {*} data
 * @returns
 */
export function ListingSupremeBonus(data) {
  return post("/Listing_SupremeBonus", { data });
}

/**
 * 公司分享收入
 * @param {*} data
 * @returns
 */
export function ListingComReferralBonus(data) {
  return post("/Listing_ComReferralBonus", { data });
}

/**
 * 公司返利收入
 * @param {*} data
 * @returns
 */
export function ListingComBonus(data) {
  return post("/Listing_ComBonus", { data });
}

/**
 * 联创分成收入
 * @param {*} data
 * @returns
 */
export function ListingUFProfitSharingBonus(data) {
  return post("/Listing_UFProfitSharingBonus", { data });
}

/**
 * 代理商收入
 * @param {*} data
 * @returns
 */
export function ListingSCProfitSharingBonus(data) {
  return post("/Listing_SCProfitSharingBonus", { data });
}

/**
 * 代理商级别
 * @param {*} data
 * @returns
 */
export function GetServiceCenterType(data) {
  return post("/GetServiceCenterType", { data });
}

/**
 * 代理商申请
 * @param {*} data
 * @returns
 */
export function RegisterSeviceCenter(data) {
  return post("/RegisterSeviceCenter", { data });
}

/**
 * 首页服务主分类栏目
 * @returns
 */
export function ServiceHomePageMainCategory(data) {
  return post("/ServiceHomePageMainCategory", { data });
}

/**
 * 服务分类和子分类列表
 * @returns
 */
export function ServiceCategory(data) {
  return post("/ServiceCategory", { data });
}

/**
 * 服务主分类，分类和子分类列表
 * @param {*} data
 * @returns
 */
export function ServiceMainCatgCategory(data) {
  return post("/ServiceMainCatgCategory", { data });
}

/**
 * 服务列表
 * @param {*} data
 * @returns
 */
export function Service(data) {
  return post("/Service", { data });
}

/**
 * 服务详情
 * @param {*} data
 * @returns
 */
export function ServiceDetails(data) {
  return post("/ServiceDetails", { data });
}

/**
 * 服务产品规格选项
 * @param {*} data
 * @returns
 */
export function ServiceProductOption(data) {
  return post("/ServiceProductOption", { data });
}

/**
 * 服务产品规格分类
 * @param {*} data
 * @returns
 */
export function ServiceProductSpecificationType(data) {
  return post("/ServiceProductSpecificationType", { data });
}

/**
 * 服务产品规格
 * @param {*} data
 * @returns
 */
export function ServiceProductSpecification(data) {
  return post("/ServiceProductSpecification", { data });
}

/**
 * 服务产品规格价格
 * @param {*} data
 * @returns
 */
export function ServiceProductSKU(data) {
  return post("/ServiceProductSKU", { data });
}

/**
 * 服务搜索
 * @param {*} data
 * @returns
 */
export function SearchService(data) {
  return post("/SearchService", { data });
}

/**
 * 爆品推荐服务
 * @param {*} data
 * @returns
 */
export function FeaturedService(data) {
  return post("/FeaturedService", { data });
}

/**
 * 推荐服务
 * @param {*} data
 * @returns
 */
export function RecommendedService(data) {
  return post("/RecommendedService", { data });
}

/**
 * 预热服务详细
 * @param {*} data
 * @returns
 */
export function PreServiceDetails(data) {
  return post("/PreServiceDetails", { data });
}

/**
 * 预热服务规格选项
 * @param {*} data
 * @returns
 */
export function PreServiceOption(data) {
  return post("/PreServiceOption", { data });
}

/**
 * 服务规格选项组合
 * @param {*} data
 * @returns
 */
export function ServiceSpecCombination(data) {
  return post("/ServiceSpecCombination", { data });
}

/**
 * 机构列表
 * @param {*} data
 * @returns
 */
export function InstitutionList(data) {
  return post("/InstitutionList", { data });
}

/**
 * 医生列表
 * @param {*} data
 * @returns
 */
export function DoctorList(data) {
  return post("/DoctorList", { data });
}

/**
 * 服务礼包列表
 * @param {*} data
 * @returns
 */
export function ServicePackage(data) {
  return post("/ServicePackage", { data });
}

/**
 * 服务礼包详细
 * @param {*} data
 * @returns
 */
export function ServicePackageSubProduct(data) {
  return post("/ServicePackageSubProduct", { data });
}

/**
 * 代理商区域
 * @param {*} data
 * @returns
 */
export function GetServiceCenterTypeArea(data) {
  return post("/GetServiceCenterTypeArea", { data });
}

/**
 * 机构合作申请
 * @param {*} data
 * @returns
 */
export function RegisterSupplier(data) {
  return post("/RegisterSupplier", { data });
}

/**
 * 微信部分支付
 * @param {*} data
 * @returns
 */
export function PartialPayment(data) {
  return post("/PartialPayment", { data });
}

/**
 * 注册公司所需金额
 * @param {*} data
 * @returns
 */
export function CheckRegCompanyDiffAmt(data) {
  return post("/CheckRegCompanyDiffAmt", { data });
}

/**
 * 省编号信息
 * @param {*} data
 * @returns
 */
export function GetStateCodeByStateName(data) {
  return post("/GetStateCodeByStateName", { data });
}

/**
 * 市编号信息
 * @param {*} data
 * @returns
 */
export function GetCityCodeByCityName(data) {
  return post("/GetCityCodeByCityName", { data });
}

/**
 * 区编号信息
 * @param {*} data
 * @returns
 */
export function GetDistrictCodeByDistrictName(data) {
  return post("/GetDistrictCodeByDistrictName", { data });
}

/**
 * 预约查询
 * @param {*} data
 * @returns
 */
export function CheckBookingDetails(data) {
  return post("/CheckBookingDetails", { data });
}

/**
 * 修改店名
 * @param {*} data
 * @returns
 */
export function UpdateMemberShopName(data) {
  return post("/Update_tblMember_ShopName", { data });
}

/**
 * 科室
 * @param {*} data
 * @returns
 */
export function DoctorDepartment(data) {
  return post("/DoctorDepartment", { data });
}

/**
 * 医生列表 (科室)
 * @param {*} data
 * @returns
 */
export function DoctorListDepartment(data) {
  return post("/DoctorListDepartment", { data });
}

/**
 * 爆品服务主分类子分类列表
 * @param {*} data
 * @returns
 */
export function ServiceCategoryListByFeaturedProduct(data) {
  return post("/ServiceCategoryListByFeaturedProduct", { data });
}

/**
 * 爆品产品主分类子分类列表
 * @param {*} data
 * @returns
 */
export function CategoryListByFeaturedProduct(data) {
  return post("/CategoryListByFeaturedProduct", { data });
}

/**
 * 爆品推荐产品 (根据分类）
 * @param {*} data
 * @returns
 */
export function FeaturedProductbyCategory(data) {
  return post("/FeaturedProductbyCategory", { data });
}

/**
 * 爆品推荐服务(根据分类）
 * @param {*} data
 * @returns
 */
export function FeaturedServiceByCategory(data) {
  return post("/FeaturedServiceByCategory", { data });
}

/**
 * 爆品推荐服务(根据分类）
 * @param {*} data
 * @returns
 */
export function ServiceCategoryLevel1(data) {
  return post("/ServiceCategoryLevel1", { data });
}

/**
 * 证件类型
 * @param {*} data
 * @returns
 */
export function IDType(data) {
  return post("/IDType", { data });
}

/**
 * 银行列表
 * @param {*} data
 * @returns
 */
export function JPBankList(data) {
  return post("/JPBankList", { data });
}

/**
 * 添加银行卡
 * @param {*} data
 * @returns
 */
export function AddBankAccInfo(data) {
  return post("/AddBankAccInfo", { data });
}

/**
 * 删除银行卡
 * @param {*} data
 * @returns
 */
export function DeleteBankAccInfo(data) {
  return post("/DeleteBankAccInfo", { data });
}

/**
 * 会员银行卡列表
 * @param {*} data
 * @returns
 */
export function MemberBankAccInfo(data) {
  return post("/MemberBankAccInfo", { data });
}

/**
 * 快捷支付验证码
 * @param {*} data
 * @returns
 */
export function JoinPayFastPaySmsRequest(data) {
  return post("/JoinPayFastPaySmsRequest", { data });
}

/**
 * 修改银行卡
 * @param {*} data
 * @returns
 */
export function EditBankAccInfo(data) {
  return post("/EditBankAccInfo", { data });
}
