/* 推荐商品组件样式 */
@import '../../common/common.less';

.recommend {
  background-color: #fff;
  padding: 30rpx 20rpx;

  .title {
    .flex(center, start);
    font-size: 35rpx;
    color: #000;
    font-weight: bold;
    margin-bottom: 30rpx;

    .line {
      width: 8rpx;
      height: 35rpx;
      background-color: #428485;
      margin-right: 20rpx;
    }
  }

  .list {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 20rpx;

    .item {
      border: 1rpx solid #f0f0f0;
      border-radius: 8rpx;
      overflow: hidden;
      background-color: #fff;

      .image {
        width: 100%;
        height: 200rpx;
        object-fit: cover;
      }

      .info {
        padding: 15rpx;

        .name {
          font-size: 28rpx;
          color: #333;
          font-weight: bold;
          margin-bottom: 10rpx;
          .ellipsis();
        }

        .price {
          .flex(center, start);

          text {
            &:first-child {
              font-size: 30rpx;
              color: #428485;
              font-weight: bold;
              margin-right: 10rpx;
            }

            &.oldPrice {
              font-size: 24rpx;
              color: #999;
              text-decoration: line-through;
            }
          }
        }
      }
    }
  }
}
