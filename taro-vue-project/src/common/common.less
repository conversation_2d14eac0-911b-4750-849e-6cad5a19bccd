/* 通用样式文件 - 从WePY项目迁移到Taro */

/* 主题色定义 */
@primary-color: #8cdac6;
@secondary-color: #0f5760;
@success-color: #0fba90;
@warning-color: #ff9500;
@error-color: #ff3333;

/* 通用flex布局混合 */
.flex(@align: center, @justify: space-between) {
  display: flex;
  align-items: @align;
  justify-content: @justify;
}

/* 文本省略号 */
.ellipsis() {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

/* 多行文本省略号 */
.ellipsis-multi(@lines: 2) {
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: @lines;
  -webkit-box-orient: vertical;
}

.btn1-bg {
  background: linear-gradient(to bottom, #1ed5ba, #0d5d51);
  border-radius: 8rpx;
}

.btn2-bg {
  background: linear-gradient(to bottom, #398db2, #1e4a5e);
  border-radius: 8rpx;
  color: #fff;
}

.btn3-bg {
  background: linear-gradient(to bottom, #a4e7e1, #68d7cd);
  border-radius: 8rpx;
  color: #273d36;
}

/* 清除浮动 */
.clearfix() {
  &::after {
    content: "";
    display: table;
    clear: both;
  }
}

/* 居中定位 */
.center-absolute() {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
}

/* 通用按钮样式 */
.btn-base() {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: 20rpx 40rpx;
  border-radius: 8rpx;
  font-size: 28rpx;
  font-weight: bold;
  border: none;
  cursor: pointer;
  transition: all 0.3s ease;

  &:active {
    opacity: 0.8;
  }

  &[disabled] {
    opacity: 0.5;
    cursor: not-allowed;
  }
}

/* 输入框样式 */
.input-base() {
  width: 100%;
  height: 80rpx;
  padding: 0 20rpx;
  border: 2rpx solid #e0e0e0;
  border-radius: 8rpx;
  font-size: 28rpx;
  background: #fff;

  &:focus {
    border-color: @primary-color;
    outline: none;
  }

  &::placeholder {
    color: #999;
  }
}

/* 页面容器 */
.page-container() {
  min-height: 100vh;
  background: #f6f6f6;
}

/* 内容容器 */
.content-container() {
  padding: 20rpx;
}

/* 动画 */
.fade-in() {
  animation: fadeIn 0.3s ease-in-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

.slide-up() {
  animation: slideUp 0.3s ease-out;
}

@keyframes slideUp {
  from {
    transform: translateY(100%);
  }
  to {
    transform: translateY(0);
  }
}

@keyframes loading {
  0% {
    background-position: 200% 0;
  }
  100% {
    background-position: -200% 0;
  }
}

taro-button-core {
  display: inline-flex !important;
  align-items: center;
  justify-content: center;
  text {
    line-height: 1;
  }
  &::after {
    content: none;
    display: none;
  }
}

/* 响应式断点 */
@mobile: ~"(max-width: 767px)";
@tablet: ~"(min-width: 768px) and (max-width: 1023px)";
@desktop: ~"(min-width: 1024px)";
